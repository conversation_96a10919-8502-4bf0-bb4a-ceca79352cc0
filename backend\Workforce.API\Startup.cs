using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.OpenApi.Models;
using Workforce.Application;
using Workforce.Infrastructure;
using Microsoft.AspNetCore.HttpLogging;

namespace Workforce.API
{
    public class Startup
    {
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        public IConfiguration Configuration { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            // Add controllers
            services.AddControllers();

            // Add application and infrastructure services
            services.AddApplication();
            services.AddInfrastructure(Configuration);

            // Add CORS policy
            services.AddCors(options =>
            {
                options.AddPolicy("AllowAll", builder =>
                {
                    builder.AllowAnyOrigin()
                           .AllowAnyMethod()
                           .AllowAnyHeader();
                });
            });

            // Register Swagger
            services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v1", new OpenApiInfo
                {
                    Title = "HireNow API",
                    Version = "v1",
                    Description = "A comprehensive part tme job listing API built with ASP.NET Core",
                    Contact = new OpenApiContact
                    {
                        Name = "HireNow API Team"
                    }
                });
            });

            // Add enhanced HTTP logging with NLog
            services.AddHttpLogging(logging =>
            {
                logging.LoggingFields = HttpLoggingFields.RequestPropertiesAndHeaders |
                                       HttpLoggingFields.ResponsePropertiesAndHeaders |
                                       HttpLoggingFields.RequestBody |
                                       HttpLoggingFields.ResponseBody;

                // Limit request body logging size
                logging.RequestBodyLogLimit = 4096;
                logging.ResponseBodyLogLimit = 4096;

                // Add sensitive headers to redact
                logging.RequestHeaders.Add("Authorization");
                logging.RequestHeaders.Add("Cookie");
                logging.ResponseHeaders.Add("Set-Cookie");
            });
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            // Add HTTP logging middleware early in the pipeline
            app.UseHttpLogging();

            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }
            
            // Enable Swagger in development and staging environments only
            if (env.IsDevelopment() || env.IsStaging())
            {
                app.UseSwagger();
                app.UseSwaggerUI(c =>
                {
                    c.SwaggerEndpoint("/swagger/v1/swagger.json", "HireNow API v1");
                    c.RoutePrefix = "swagger"; // Set Swagger UI at /swagger
                    c.DocumentTitle = "HireNow API Documentation";
                    c.DefaultModelsExpandDepth(-1); // Disable swagger schemas section
                });
            }
            
            app.UseRouting();

            app.UseCors("AllowAll");

            app.UseAuthorization();

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
            });
        }
    }
}
