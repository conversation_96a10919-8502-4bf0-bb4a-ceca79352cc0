using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using System.Collections.Concurrent;

namespace Workforce.Application.Services
{
    /// <summary>
    /// Service for rate limiting operations using in-memory cache with thread-safe operations
    /// </summary>
    public class RateLimitingService : IRateLimitingService, IDisposable
    {
        private readonly IMemoryCache _cache;
        private readonly int _ipMaxAttempts;
        private readonly int _emailMaxAttempts;
        private readonly TimeSpan _ipWindowDuration;
        private readonly TimeSpan _emailWindowDuration;

        // Thread-safe per-key locking mechanism
        private readonly ConcurrentDictionary<string, SemaphoreSlim> _keyLocks = new();
        private readonly SemaphoreSlim _lockCleanupSemaphore = new(1, 1);
        private bool _disposed;

        public RateLimitingService(IMemoryCache cache, IConfiguration configuration)
        {
            _cache = cache;
            _ipMaxAttempts = int.TryParse(configuration["SecuritySettings:RateLimit:IpMaxAttempts"], out var ipMax) ? ipMax : 10;
            _emailMaxAttempts = int.TryParse(configuration["SecuritySettings:RateLimit:EmailMaxAttempts"], out var emailMax) ? emailMax : 5;
            _ipWindowDuration = TimeSpan.FromMinutes(int.TryParse(configuration["SecuritySettings:RateLimit:IpWindowMinutes"], out var ipWindow) ? ipWindow : 1);
            _emailWindowDuration = TimeSpan.FromMinutes(int.TryParse(configuration["SecuritySettings:RateLimit:EmailWindowMinutes"], out var emailWindow) ? emailWindow : 1);
        }

        /// <summary>
        /// Gets or creates a semaphore for the specified key to ensure thread-safe operations
        /// </summary>
        /// <param name="key">The cache key</param>
        /// <returns>A semaphore for the key</returns>
        private SemaphoreSlim GetKeyLock(string key)
        {
            return _keyLocks.GetOrAdd(key, _ => new SemaphoreSlim(1, 1));
        }

        /// <summary>
        /// Generates a consistent cache key for IP-based rate limiting
        /// </summary>
        /// <param name="ipAddress">The IP address</param>
        /// <returns>A consistent cache key</returns>
        private static string GetIpCacheKey(string ipAddress)
        {
            return $"ip_login_attempts:{ipAddress?.Replace(":", "_").Replace(".", "_")}";
        }

        /// <summary>
        /// Generates a consistent cache key for email-based rate limiting
        /// </summary>
        /// <param name="email">The email address</param>
        /// <returns>A consistent cache key</returns>
        private static string GetEmailCacheKey(string email)
        {
            return $"email_login_attempts:{email.ToLowerInvariant()}";
        }

        /// <summary>
        /// Safely gets and updates attempts list with thread synchronization
        /// </summary>
        /// <param name="key">The cache key</param>
        /// <param name="windowDuration">The time window for attempts</param>
        /// <param name="addNewAttempt">Whether to add a new attempt</param>
        /// <returns>The filtered attempts list</returns>
        private async Task<List<DateTime>> GetAndUpdateAttemptsAsync(string key, TimeSpan windowDuration, bool addNewAttempt = false)
        {
            var keyLock = GetKeyLock(key);
            await keyLock.WaitAsync();

            try
            {
                var attempts = _cache.Get<List<DateTime>>(key) ?? [];

                // Remove expired attempts
                var cutoff = DateTime.UtcNow.Subtract(windowDuration);
                attempts = attempts.Where(a => a > cutoff).ToList();

                // Add new attempt if requested
                if (addNewAttempt)
                {
                    attempts.Add(DateTime.UtcNow);
                }

                // Update cache with filtered/updated attempts
                _cache.Set(key, attempts, windowDuration);

                return attempts;
            }
            finally
            {
                keyLock.Release();
            }
        }

        /// <summary>
        /// Checks if the IP address has exceeded the rate limit for login attempts
        /// </summary>
        /// <param name="ipAddress">The IP address to check</param>
        /// <returns>True if rate limit is exceeded, false otherwise</returns>
        public async Task<bool> IsIpRateLimitExceededAsync(string ipAddress)
        {
            var key = GetIpCacheKey(ipAddress);
            var attempts = await GetAndUpdateAttemptsAsync(key, _ipWindowDuration, addNewAttempt: false);

            return attempts.Count >= _ipMaxAttempts;
        }

        /// <summary>
        /// Checks if the email has exceeded the rate limit for login attempts
        /// </summary>
        /// <param name="email">The email to check</param>
        /// <returns>True if rate limit is exceeded, false otherwise</returns>
        public async Task<bool> IsEmailRateLimitExceededAsync(string email)
        {
            var key = GetEmailCacheKey(email);
            var attempts = await GetAndUpdateAttemptsAsync(key, _emailWindowDuration, addNewAttempt: false);

            return attempts.Count >= _emailMaxAttempts;
        }

        /// <summary>
        /// Records a login attempt for IP-based rate limiting
        /// </summary>
        /// <param name="ipAddress">The IP address</param>
        public async Task RecordIpLoginAttemptAsync(string ipAddress)
        {
            var key = GetIpCacheKey(ipAddress);
            await GetAndUpdateAttemptsAsync(key, _ipWindowDuration, addNewAttempt: true);
        }

        /// <summary>
        /// Records a login attempt for email-based rate limiting
        /// </summary>
        /// <param name="email">The email address</param>
        public async Task RecordEmailLoginAttemptAsync(string email)
        {
            var key = GetEmailCacheKey(email);
            await GetAndUpdateAttemptsAsync(key, _emailWindowDuration, addNewAttempt: true);
        }

        /// <summary>
        /// Gets the remaining time until the IP rate limit resets
        /// </summary>
        /// <param name="ipAddress">The IP address</param>
        /// <returns>Time remaining until reset</returns>
        public async Task<TimeSpan> GetIpRateLimitResetTimeAsync(string ipAddress)
        {
            var key = GetIpCacheKey(ipAddress);
            var attempts = await GetAndUpdateAttemptsAsync(key, _ipWindowDuration, addNewAttempt: false);

            if (attempts.Count == 0)
                return TimeSpan.Zero;

            var oldestAttempt = attempts.Min();
            var resetTime = oldestAttempt.Add(_ipWindowDuration);
            var remaining = resetTime.Subtract(DateTime.UtcNow);

            return remaining > TimeSpan.Zero ? remaining : TimeSpan.Zero;
        }

        /// <summary>
        /// Gets the remaining time until the email rate limit resets
        /// </summary>
        /// <param name="email">The email address</param>
        /// <returns>Time remaining until reset</returns>
        public async Task<TimeSpan> GetEmailRateLimitResetTimeAsync(string email)
        {
            var key = GetEmailCacheKey(email);
            var attempts = await GetAndUpdateAttemptsAsync(key, _emailWindowDuration, addNewAttempt: false);

            if (attempts.Count == 0)
                return TimeSpan.Zero;

            var oldestAttempt = attempts.Min();
            var resetTime = oldestAttempt.Add(_emailWindowDuration);
            var remaining = resetTime.Subtract(DateTime.UtcNow);

            return remaining > TimeSpan.Zero ? remaining : TimeSpan.Zero;
        }

        /// <summary>
        /// Disposes of the service and cleans up resources
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Protected dispose method for proper disposal pattern
        /// </summary>
        /// <param name="disposing">Whether disposing is called from Dispose method</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                // Dispose all semaphores
                foreach (var semaphore in _keyLocks.Values)
                {
                    semaphore?.Dispose();
                }
                _keyLocks.Clear();

                _lockCleanupSemaphore?.Dispose();
                _disposed = true;
            }
        }
    }
}
