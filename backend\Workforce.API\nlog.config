<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      autoReload="true"
      internalLogLevel="Info"
      internalLogFile="c:\temp\internal-nlog-AspNetCore.txt">

  <!-- enable asp.net core layout renderers -->
  <extensions>
    <add assembly="NLog.Web.AspNetCore"/>
  </extensions>

  <!-- the targets to write to -->
  <targets>
    <!-- File Target for all log messages with basic details -->
    <target xsi:type="File" name="allfile" fileName="logs/nlog-AspNetCore-all-${shortdate}.log"
            layout="${longdate}|${event-properties:item=EventId:whenEmpty=0}|${level:uppercase=true}|${logger}|${message} ${exception:format=tostring}" />

    <!-- File Target for own log messages with extra web details using some ASP.NET core renderers -->
    <target xsi:type="File" name="ownFile-web" fileName="logs/nlog-AspNetCore-own-${shortdate}.log"
            layout="${longdate}|${event-properties:item=EventId:whenEmpty=0}|${level:uppercase=true}|${logger}|${message} ${exception:format=tostring}|url: ${aspnet-request-url}|action: ${aspnet-mvc-action}|${callsite}" />

    <!-- File Target for audit logs -->
    <target xsi:type="File" name="auditFile" fileName="logs/audit-${shortdate}.log"
            layout="${longdate}|${level:uppercase=true}|${logger}|${message}|${aspnet-request-ip}|${aspnet-request-useragent}" />

    <!-- File Target for request logs -->
    <target xsi:type="File" name="requestFile" fileName="logs/requests-${shortdate}.log"
            layout="${longdate}|${aspnet-request-method}|${aspnet-request-url}|${aspnet-response-statuscode}|${aspnet-request-ip}|${aspnet-request-useragent}|${aspnet-TraceIdentifier}|Duration: ${event-properties:item=ElapsedMilliseconds}ms" />

    <!-- Console Target for development -->
    <target xsi:type="Console" name="lifetimeConsole" layout="${level:truncate=4:tolower=true}\: ${logger}[0]${newline}      ${message}${exception:format=tostring}" />
  </targets>

  <!-- rules to map from logger name to target -->
  <rules>
    <!-- All logs, including from Microsoft -->
    <logger name="*" minlevel="Trace" writeTo="allfile" />

    <!-- Output hosting lifetime messages to console target for faster startup detection -->
    <logger name="Microsoft.Hosting.Lifetime" minlevel="Info" writeTo="lifetimeConsole, ownFile-web" final="true" />

    <!-- Skip non-critical Microsoft logs and so log only own logs (BlackHole) -->
    <logger name="Microsoft.*" maxlevel="Info" final="true" />
    <logger name="System.Net.Http.*" maxlevel="Info" final="true" />

    <!-- Audit logs -->
    <logger name="Workforce.Application.Services.AuditLoggingService" minlevel="Info" writeTo="auditFile" />

    <!-- Request logs -->
    <logger name="Microsoft.AspNetCore.HttpLogging.HttpLoggingMiddleware" minlevel="Info" writeTo="requestFile" />

    <!-- Application logs -->
    <logger name="Workforce.*" minlevel="Trace" writeTo="ownFile-web" />
  </rules>
</nlog>
